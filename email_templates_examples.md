# Mẫu Email Templates ch<PERSON> <PERSON><PERSON> SMTP Config

## 1. Template <PERSON><PERSON>ô<PERSON> <PERSON><PERSON><PERSON> (Login Notification)

### Subject:
```
🔐 Thông báo đăng nhập - {title}
```

### Content:
```html
<div style="background-color:#f5f5f5; font-family:'<PERSON>o',sans-serif; padding:20px">
<div style="background-color:#ffffff; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1); margin:0 auto; max-width:600px; padding:30px">

<h2 style="color:#333; margin-bottom:20px">{title}</h2>
<h3 style="color:#007bff; margin-bottom:15px">🔐 Thông báo đăng nhập</h3>
<p style="color:#28a745; font-weight:bold; margin-bottom:15px">✅ Đăng nhập thành công!</p>

<p><PERSON><PERSON> chào <strong>{username}</strong>, tà<PERSON> khoản của bạn vừa được đăng nhập thành công.</p>

<h4 style="color:#333; margin-top:25px; margin-bottom:15px">📱 Thông tin thiết bị & phiên đăng nhập:</h4>
<table style="width:100%; border-collapse:collapse; margin-bottom:20px">
<tr><td style="padding:8px 0; border-bottom:1px solid #eee"><strong>🕒 Thời gian:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee">{time}</td></tr>
<tr><td style="padding:8px 0; border-bottom:1px solid #eee"><strong>💻 Thiết bị:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee">{device}</td></tr>
<tr><td style="padding:8px 0; border-bottom:1px solid #eee"><strong>📍 IP Address:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee">{ip}</td></tr>
</table>

<div style="background-color:#fff3cd; border:1px solid #ffeaa7; border-radius:5px; padding:15px; margin:20px 0">
<h4 style="color:#856404; margin-top:0">🛡️ Bảo mật tài khoản:</h4>
<p style="color:#856404; margin-bottom:10px">Nếu đây <strong>KHÔNG</strong> phải là bạn đăng nhập:</p>
<ul style="color:#856404; margin:0; padding-left:20px">
<li>Hãy thay đổi mật khẩu ngay lập tức</li>
<li>Kiểm tra và cập nhật cài đặt bảo mật</li>
<li>Liên hệ với chúng tôi để được hỗ trợ</li>
</ul>
</div>

<div style="background-color:#d1ecf1; border:1px solid #bee5eb; border-radius:5px; padding:15px; margin:20px 0">
<p style="color:#0c5460; margin:0"><strong>💡 Mẹo bảo mật:</strong> Để bảo vệ tài khoản, hãy luôn đăng xuất sau khi sử dụng và không chia sẻ thông tin đăng nhập với bất kỳ ai.</p>
</div>

<hr style="border:none; border-top:1px solid #eee; margin:25px 0">

<div style="text-align:center; color:#666; font-size:14px">
<p><strong>{title}</strong></p>
<p>Website: <a href="https://{domain}" style="color:#007bff">{domain}</a></p>
<p style="margin-top:15px; font-style:italic">Email này được gửi tự động để đảm bảo bảo mật tài khoản của bạn.</p>
</div>

</div>
</div>
```

## 2. Template Khôi Phục Mật Khẩu (Password Reset)

### Subject:
```
🔑 Yêu cầu khôi phục mật khẩu - {title}
```

### Content:
```html
<div style="background-color:#f5f5f5; font-family:'Roboto',sans-serif; padding:20px">
<div style="background-color:#ffffff; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1); margin:0 auto; max-width:600px; padding:30px">

<h2 style="color:#333; margin-bottom:20px">{title}</h2>
<h3 style="color:#dc3545; margin-bottom:15px">🔑 Khôi phục mật khẩu</h3>

<p>Xin chào <strong>{username}</strong>,</p>
<p>Chúng tôi nhận được yêu cầu khôi phục mật khẩu cho tài khoản của bạn trên <strong>{domain}</strong>.</p>

<div style="text-align:center; margin:25px 0">
<a href="{link}" style="background-color:#007bff; color:white; padding:12px 25px; text-decoration:none; border-radius:5px; display:inline-block; font-weight:bold">🔗 Đặt lại mật khẩu</a>
</div>

<div style="background-color:#f8d7da; border:1px solid #f5c6cb; border-radius:5px; padding:15px; margin:20px 0">
<p style="color:#721c24; margin:0"><strong>⚠️ Lưu ý:</strong> Link này chỉ có hiệu lực trong 60 phút. Nếu bạn không yêu cầu khôi phục mật khẩu, vui lòng bỏ qua email này.</p>
</div>

<h4 style="color:#333; margin-top:25px; margin-bottom:15px">📱 Thông tin yêu cầu:</h4>
<table style="width:100%; border-collapse:collapse; margin-bottom:20px">
<tr><td style="padding:8px 0; border-bottom:1px solid #eee"><strong>🕒 Thời gian:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee">{time}</td></tr>
<tr><td style="padding:8px 0; border-bottom:1px solid #eee"><strong>💻 Thiết bị:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee">{device}</td></tr>
<tr><td style="padding:8px 0; border-bottom:1px solid #eee"><strong>📍 IP Address:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee">{ip}</td></tr>
</table>

<hr style="border:none; border-top:1px solid #eee; margin:25px 0">

<div style="text-align:center; color:#666; font-size:14px">
<p><strong>{title}</strong></p>
<p>Website: <a href="https://{domain}" style="color:#007bff">{domain}</a></p>
<p style="margin-top:15px; font-style:italic">Email này được gửi tự động theo yêu cầu khôi phục mật khẩu.</p>
</div>

</div>
</div>
```

## 3. Template Giao Hàng Sản Phẩm (Product Delivery)

### Subject:
```
📦 Thông báo giao hàng sản phẩm - {title}
```

### Content:
```html
<div style="background-color:#f5f5f5; font-family:'Roboto',sans-serif; padding:20px">
<div style="background-color:#ffffff; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1); margin:0 auto; max-width:600px; padding:30px">

<h2 style="color:#333; margin-bottom:20px">{title}</h2>
<h3 style="color:#28a745; margin-bottom:15px">📦 Thông báo giao hàng sản phẩm</h3>
<p style="color:#28a745; font-weight:bold; margin-bottom:15px">✅ Đơn hàng đã được xử lý thành công!</p>

<p>Chào <strong>{username}</strong>,</p>
<p>Đơn hàng <strong>#{order_id}</strong> của bạn đã được xử lý thành công!</p>

<div style="background-color:#d4edda; border:1px solid #c3e6cb; border-radius:5px; padding:20px; margin:20px 0">
<h4 style="color:#155724; margin-top:0; margin-bottom:15px">📋 Thông tin sản phẩm:</h4>
<table style="width:100%; border-collapse:collapse">
<tr><td style="padding:8px 0; border-bottom:1px solid #c3e6cb; color:#155724"><strong>📦 Tên sản phẩm:</strong></td><td style="padding:8px 0; border-bottom:1px solid #c3e6cb; color:#155724"><strong>{product_name}</strong></td></tr>
<tr><td style="padding:8px 0; border-bottom:1px solid #c3e6cb; color:#155724"><strong>💾 Dữ liệu:</strong></td><td style="padding:8px 0; border-bottom:1px solid #c3e6cb; color:#155724">{product_data}</td></tr>
<tr><td style="padding:8px 0; color:#155724"><strong>🕒 Thời gian giao hàng:</strong></td><td style="padding:8px 0; color:#155724">{time}</td></tr>
</table>
</div>

<div style="background-color:#fff3cd; border:1px solid #ffeaa7; border-radius:5px; padding:15px; margin:20px 0">
<p style="color:#856404; margin:0"><strong>💡 Lưu ý:</strong> Vui lòng kiểm tra kỹ thông tin sản phẩm. Nếu có bất kỳ vấn đề gì, hãy liên hệ với chúng tôi ngay lập tức.</p>
</div>

<p style="margin-top:25px">Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</p>

<hr style="border:none; border-top:1px solid #eee; margin:25px 0">

<div style="text-align:center; color:#666; font-size:14px">
<p>Trân trọng,<br><strong>Đội ngũ hỗ trợ của {title}</strong></p>
<p>Website: <a href="https://{domain}" style="color:#007bff">{domain}</a></p>
<p style="margin-top:15px; font-style:italic">Email này được gửi tự động khi đơn hàng được xử lý.</p>
</div>

</div>
</div>
```

## Các biến có thể sử dụng:

### Login Notification:
- `{domain}` => Link Website
- `{title}` => Tên website  
- `{username}` => Tên khách hàng
- `{ip}` => Địa chỉ IP
- `{device}` => Thiết bị
- `{time}` => Thời gian

### Password Reset:
- `{domain}` => Link Website
- `{title}` => Tên website
- `{username}` => Tên khách hàng
- `{link}` => Link xác minh
- `{ip}` => Địa chỉ IP
- `{device}` => Thiết bị
- `{time}` => Thời gian

### Product Delivery:
- `{domain}` => Link Website
- `{title}` => Tên website
- `{username}` => Tên khách hàng
- `{product_name}` => Tên sản phẩm
- `{product_data}` => Dữ liệu sản phẩm
- `{order_id}` => Mã đơn hàng
- `{time}` => Thời gian

## Lưu ý quan trọng:
1. **Sử dụng `{variable}` thay vì `{{ $variable }}`**
2. **Không sử dụng các biến không có trong danh sách**
3. **Subject không được để trống nếu muốn bật email**
4. **Content phải là HTML hợp lệ**
